# File Management System – Frontend

This is the frontend for the File Management System, built with:

- [React](https://react.dev/)
- [TypeScript](https://www.typescriptlang.org/)
- [Vite](https://vitejs.dev/)
- [Tailwind CSS](https://tailwindcss.com/)
- [shadcn/ui](https://ui.shadcn.dev/)

---

## 🚀 Getting Started

### 1. Install Dependencies

```bash
cd frontend
npm install
```

### 2. Run the server

```bash
npm run dev
```

The app will be available at: http://localhost:5173

### Using shadcn/ui Components
To generate a UI component (e.g., Tabs), run:
```bash
npx shadcn-ui@latest add tabs
```
You can browse all available components here: https://ui.shadcn.com/docs/components
