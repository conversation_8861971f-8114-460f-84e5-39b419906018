terraform {
  required_version = ">= 1.0"
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 6.0"
    }
  }

  backend "gcs" {
    bucket  = "tf-state-backend-practice"      # Same bucket as global
    prefix  = "terraform/production"  # Different prefix for production
  }
}

provider "google" {
  project = var.project_id
  region  = var.region
  zone    = var.zone
}

# Enable required APIs
resource "google_project_service" "container_api" {
  service = "container.googleapis.com"

  disable_dependent_services = true
}

resource "google_project_service" "compute_api" {
  service = "compute.googleapis.com"

  disable_dependent_services = true
}

# VPC Module
module "vpc" {
  source = "../../modules/vpc"

  project_id   = var.project_id
  region       = var.region
  network_name = "${var.cluster_name}-vpc"
  subnet_name  = "${var.cluster_name}-subnet"

  # Production environment CIDR blocks (different from dev)
  subnet_cidr   = "********/24"
  pods_cidr     = "192.168.68.0/22"
  services_cidr = "192.168.2.0/24"

  enable_private_google_access = true
  enable_flow_logs            = true  # Enabled for production monitoring

  tags = {
    environment = var.environment
    managed_by  = "terraform"
  }

  depends_on = [
    google_project_service.compute_api
  ]
}

# GKE Cluster Module - Production Configuration
module "gke" {
  source = "../../modules/gke"

  project_id   = var.project_id
  region       = var.region
  cluster_name = var.cluster_name
  environment  = var.environment

  # Network configuration
  network_name         = module.vpc.network_name
  subnet_name          = module.vpc.subnet_name
  pods_range_name      = module.vpc.pods_range_name
  services_range_name  = module.vpc.services_range_name

  # Node pool configuration - Production settings
  node_count     = var.node_count
  min_node_count = 2  # Higher minimum for production
  max_node_count = 10 # Higher maximum for production scaling
  machine_type   = var.machine_type
  disk_size_gb   = var.disk_size_gb
  disk_type      = "pd-ssd"  # SSD for better performance
  preemptible    = false     # Use regular instances for reliability

  # Cluster configuration - Production settings
  enable_private_nodes        = true
  enable_private_endpoint     = false  # Set to true for more security
  master_ipv4_cidr_block     = "**********/28"
  enable_shielded_nodes      = true   # Enhanced security
  enable_workload_identity   = true
  enable_network_policy      = true
  enable_resource_usage_export = true  # Enable monitoring
  bigquery_dataset_id        = "gke_usage_metering"

  # Master authorized networks for production security
  master_authorized_networks = [
    {
      cidr_block   = "********/24"  # Allow from our subnet
      display_name = "VPC"
    }
  ]

  maintenance_start_time = "03:00"

  labels = {
    environment = var.environment
    managed_by  = "terraform"
  }

  depends_on = [
    google_project_service.container_api,
    google_project_service.compute_api,
    module.vpc
  ]
}

