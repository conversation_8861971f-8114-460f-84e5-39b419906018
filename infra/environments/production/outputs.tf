# GKE Cluster Outputs
output "cluster_name" {
  description = "GKE cluster name"
  value       = module.gke.cluster_name
}

output "cluster_endpoint" {
  description = "GKE cluster endpoint"
  value       = module.gke.cluster_endpoint
  sensitive   = true
}

output "cluster_location" {
  description = "GKE cluster location"
  value       = module.gke.cluster_location
}

output "cluster_ca_certificate" {
  description = "GKE cluster CA certificate"
  value       = module.gke.cluster_ca_certificate
  sensitive   = true
}

output "service_account_email" {
  description = "Service account email for GKE nodes"
  value       = module.gke.service_account_email
}

output "kubectl_config_command" {
  description = "Command to configure kubectl"
  value       = module.gke.kubectl_config_command
}

# VPC Outputs
output "vpc_network_name" {
  description = "VPC network name"
  value       = module.vpc.network_name
}

output "subnet_name" {
  description = "Subnet name"
  value       = module.vpc.subnet_name
}

output "subnet_cidr" {
  description = "Subnet CIDR block"
  value       = module.vpc.subnet_cidr
}

output "pods_cidr" {
  description = "Pods CIDR block"
  value       = module.vpc.pods_cidr
}

output "services_cidr" {
  description = "Services CIDR block"
  value       = module.vpc.services_cidr
}

output "nat_gateway_name" {
  description = "Cloud NAT gateway name"
  value       = module.vpc.nat_name
}