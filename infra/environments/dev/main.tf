terraform {
  required_version = ">= 1.0"
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 6.0"
    }
  }

  backend "gcs" {
    bucket = "tf-state-backend-practice"
    prefix = "terraform/dev"
  }
}

provider "google" {
  project = var.project_id
  region  = var.region
  zone    = var.zone
}

# Enable required APIs
resource "google_project_service" "container_api" {
  service = "container.googleapis.com"

  disable_dependent_services = true
}

resource "google_project_service" "compute_api" {
  service = "compute.googleapis.com"

  disable_dependent_services = true
}

# VPC Module
module "vpc" {
  source = "../../modules/vpc"

  project_id   = var.project_id
  region       = var.region
  network_name = "${var.cluster_name}-vpc"
  subnet_name  = "${var.cluster_name}-subnet"

  # Dev environment CIDR blocks
  subnet_cidr   = "10.0.0.0/24"
  pods_cidr     = "192.168.64.0/22"
  services_cidr = "192.168.1.0/24"

  enable_private_google_access = true
  enable_flow_logs             = false # Disabled for cost savings in dev

  tags = {
    environment = var.environment
    managed_by  = "terraform"
  }

  depends_on = [
    google_project_service.compute_api
  ]
}

# GKE Cluster Module
module "gke" {
  source = "../../modules/gke"

  project_id   = var.project_id
  region       = var.region
  cluster_name = var.cluster_name
  environment  = var.environment

  # Network configuration
  network_name        = module.vpc.network_name
  subnet_name         = module.vpc.subnet_name
  pods_range_name     = module.vpc.pods_range_name
  services_range_name = module.vpc.services_range_name

  # Node pool configuration - Dev settings
  node_count     = var.node_count
  min_node_count = 1
  max_node_count = 5
  machine_type   = var.machine_type
  disk_size_gb   = var.disk_size_gb
  disk_type      = "pd-standard" # Standard disk for cost savings
  preemptible    = true          # Use preemptible instances for cost savings

  # Cluster configuration - Dev settings
  enable_private_nodes         = false # Public nodes for easier access in dev
  enable_private_endpoint      = false
  enable_shielded_nodes        = false # Disabled for cost savings
  enable_workload_identity     = true
  enable_network_policy        = true
  enable_resource_usage_export = false # Disabled for cost savings

  maintenance_start_time = "03:00"

  labels = {
    environment = var.environment
    managed_by  = "terraform"
  }

  depends_on = [
    google_project_service.container_api,
    google_project_service.compute_api,
    module.vpc
  ]
}

