# Example Terraform Variables Configuration
# Copy this file to terraform.tfvars in each environment directory and customize

# Google Cloud Project Configuration
project_id = "your-gcp-project-id"
region     = "us-central1"
zone       = "us-central1-a"

# Development Environment Example
# Copy to infra/environments/dev/terraform.tfvars
# cluster_name      = "file-management-dev"
# node_count        = 2
# machine_type      = "e2-medium"
# disk_size_gb      = 20
# environment       = "dev"
# enable_autopilot  = true   # Recommended for dev to avoid quota issues

# Production Environment Example
# Copy to infra/environments/production/terraform.tfvars
# cluster_name      = "file-management-prod"
# node_count        = 3
# machine_type      = "e2-standard-2"
# disk_size_gb      = 50
# environment       = "production"
# enable_autopilot  = false  # Standard cluster for production control

# Alternative Regions (uncomment and modify as needed)
# region = "us-east1"
# zone   = "us-east1-a"

# region = "europe-west1"
# zone   = "europe-west1-a"

# region = "asia-southeast1"
# zone   = "asia-southeast1-a"
