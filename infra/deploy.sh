#!/bin/bash

# File Management System - GKE Deployment Script
# This script helps deploy GKE clusters for different environments

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check if gcloud is installed
    if ! command -v gcloud &> /dev/null; then
        print_error "gcloud CLI is not installed. Please install it first."
        exit 1
    fi
    
    # Check if terraform is installed
    if ! command -v terraform &> /dev/null; then
        print_error "Terraform is not installed. Please install it first."
        exit 1
    fi
    
    # Check if kubectl is installed
    if ! command -v kubectl &> /dev/null; then
        print_warning "kubectl is not installed. You'll need it to manage the cluster."
    fi
    
    print_success "Prerequisites check completed"
}

# Function to validate environment
validate_environment() {
    local env=$1
    if [[ "$env" != "dev" && "$env" != "production" ]]; then
        print_error "Invalid environment. Use 'dev' or 'production'"
        exit 1
    fi
}

# Function to deploy environment
deploy_environment() {
    local env=$1
    local action=${2:-apply}
    
    validate_environment "$env"
    
    print_status "Deploying $env environment..."
    
    cd "environments/$env"
    
    # Check if terraform.tfvars exists
    if [[ ! -f "terraform.tfvars" ]]; then
        print_warning "terraform.tfvars not found. Please create it from terraform.tfvars.example"
        print_status "You can copy the example file:"
        echo "cp ../../terraform.tfvars.example terraform.tfvars"
        echo "Then edit terraform.tfvars with your project settings"
        exit 1
    fi
    
    # Initialize Terraform
    print_status "Initializing Terraform..."
    terraform init
    
    # Validate configuration
    print_status "Validating Terraform configuration..."
    terraform validate
    
    # Plan deployment
    print_status "Creating Terraform plan..."
    terraform plan -out="tfplan"
    
    if [[ "$action" == "plan" ]]; then
        print_success "Plan created successfully. Review the plan above."
        cd - > /dev/null
        return 0
    fi
    
    # Apply deployment
    print_status "Applying Terraform configuration..."
    terraform apply "tfplan"
    
    # Get cluster credentials
    print_status "Configuring kubectl..."
    local cluster_name=$(terraform output -raw cluster_name)
    local cluster_location=$(terraform output -raw cluster_location)
    local project_id=$(terraform output -raw project_id 2>/dev/null || echo "")
    
    if [[ -n "$project_id" ]]; then
        gcloud container clusters get-credentials "$cluster_name" --region "$cluster_location" --project "$project_id"
    else
        print_warning "Could not automatically configure kubectl. Run this command manually:"
        echo "gcloud container clusters get-credentials $cluster_name --region $cluster_location --project YOUR_PROJECT_ID"
    fi
    
    print_success "$env environment deployed successfully!"
    
    cd - > /dev/null
}

# Function to destroy environment
destroy_environment() {
    local env=$1
    
    validate_environment "$env"
    
    print_warning "This will DESTROY all resources in the $env environment!"
    read -p "Are you sure? Type 'yes' to confirm: " confirm
    
    if [[ "$confirm" != "yes" ]]; then
        print_status "Deployment cancelled"
        exit 0
    fi
    
    cd "environments/$env"
    
    print_status "Destroying $env environment..."
    terraform destroy
    
    print_success "$env environment destroyed"
    
    cd - > /dev/null
}

# Function to show help
show_help() {
    echo "File Management System - GKE Deployment Script"
    echo ""
    echo "Usage: $0 <command> [environment]"
    echo ""
    echo "Commands:"
    echo "  deploy <env>     Deploy the specified environment (dev|production)"
    echo "  plan <env>       Show deployment plan for the specified environment"
    echo "  destroy <env>    Destroy the specified environment"
    echo "  status <env>     Show status of the specified environment"
    echo "  help             Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 deploy dev         # Deploy development environment"
    echo "  $0 plan production    # Show production deployment plan"
    echo "  $0 destroy dev        # Destroy development environment"
    echo "  $0 status production  # Show production environment status"
}

# Function to show environment status
show_status() {
    local env=$1
    
    validate_environment "$env"
    
    cd "environments/$env"
    
    print_status "Status for $env environment:"
    
    if [[ -f ".terraform/terraform.tfstate" ]]; then
        terraform show
    else
        print_warning "No Terraform state found. Environment may not be deployed."
    fi
    
    cd - > /dev/null
}

# Main script logic
main() {
    local command=$1
    local environment=$2
    
    case "$command" in
        "deploy")
            check_prerequisites
            deploy_environment "$environment" "apply"
            ;;
        "plan")
            check_prerequisites
            deploy_environment "$environment" "plan"
            ;;
        "destroy")
            check_prerequisites
            destroy_environment "$environment"
            ;;
        "status")
            show_status "$environment"
            ;;
        "help"|"--help"|"-h"|"")
            show_help
            ;;
        *)
            print_error "Unknown command: $command"
            show_help
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
