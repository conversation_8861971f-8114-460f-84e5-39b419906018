# Example: Separate buckets approach

# For dev environment
terraform {
  backend "gcs" {
    bucket  = "tf-state-dev"
    prefix  = "terraform/state"
  }
}

# For production environment  
terraform {
  backend "gcs" {
    bucket  = "tf-state-production"
    prefix  = "terraform/state"
  }
}

# For global environment
terraform {
  backend "gcs" {
    bucket  = "tf-state-global"
    prefix  = "terraform/state"
  }
}
