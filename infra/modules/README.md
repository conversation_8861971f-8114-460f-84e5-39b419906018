# Terraform Modules

This directory contains reusable Terraform modules for the File Management System infrastructure.

## 📦 Available Modules

### VPC Module (`vpc/`)

Creates a VPC network with subnets and networking components optimized for GKE.

#### Usage

```hcl
module "vpc" {
  source = "../../modules/vpc"
  
  project_id   = "my-project-id"
  region       = "us-central1"
  network_name = "my-vpc"
  subnet_name  = "my-subnet"
  
  # CIDR configuration
  subnet_cidr   = "10.0.0.0/24"
  pods_cidr     = "************/22"
  services_cidr = "***********/24"
  
  # Optional features
  enable_private_google_access = true
  enable_flow_logs            = false
  
  tags = {
    environment = "dev"
    managed_by  = "terraform"
  }
}
```

#### Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| project_id | The GCP project ID | `string` | n/a | yes |
| region | The GCP region for resources | `string` | n/a | yes |
| network_name | Name of the VPC network | `string` | n/a | yes |
| subnet_name | Name of the subnet | `string` | n/a | yes |
| subnet_cidr | CIDR block for the subnet | `string` | `"10.0.0.0/24"` | no |
| pods_cidr | CIDR block for pods secondary range | `string` | `"************/22"` | no |
| services_cidr | CIDR block for services secondary range | `string` | `"***********/24"` | no |
| enable_private_google_access | Enable private Google access | `bool` | `true` | no |
| enable_flow_logs | Enable VPC flow logs | `bool` | `false` | no |

#### Outputs

| Name | Description |
|------|-------------|
| network_name | Name of the VPC network |
| subnet_name | Name of the subnet |
| pods_range_name | Name of the pods secondary range |
| services_range_name | Name of the services secondary range |
| router_name | Name of the Cloud Router |
| nat_name | Name of the Cloud NAT |

### GKE Module (`gke/`)

Creates a GKE cluster with managed node pools and security configurations.

#### Usage

```hcl
module "gke" {
  source = "../../modules/gke"
  
  project_id   = "my-project-id"
  region       = "us-central1"
  cluster_name = "my-cluster"
  environment  = "dev"
  
  # Network configuration
  network_name         = module.vpc.network_name
  subnet_name          = module.vpc.subnet_name
  pods_range_name      = module.vpc.pods_range_name
  services_range_name  = module.vpc.services_range_name
  
  # Node pool configuration
  node_count     = 3
  min_node_count = 1
  max_node_count = 10
  machine_type   = "e2-medium"
  disk_size_gb   = 20
  preemptible    = true
  
  # Security configuration
  enable_private_nodes     = false
  enable_shielded_nodes   = false
  enable_workload_identity = true
  enable_network_policy   = true
}
```

#### Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| project_id | The GCP project ID | `string` | n/a | yes |
| region | The GCP region for the cluster | `string` | n/a | yes |
| cluster_name | Name of the GKE cluster | `string` | n/a | yes |
| environment | Environment name | `string` | n/a | yes |
| network_name | Name of the VPC network | `string` | n/a | yes |
| subnet_name | Name of the subnet | `string` | n/a | yes |
| node_count | Number of nodes in the node pool | `number` | `3` | no |
| machine_type | Machine type for GKE nodes | `string` | `"e2-medium"` | no |
| preemptible | Use preemptible instances | `bool` | `false` | no |
| enable_private_nodes | Enable private nodes | `bool` | `false` | no |
| enable_shielded_nodes | Enable shielded VM features | `bool` | `false` | no |

#### Outputs

| Name | Description |
|------|-------------|
| cluster_name | GKE cluster name |
| cluster_endpoint | GKE cluster endpoint |
| cluster_ca_certificate | GKE cluster CA certificate |
| service_account_email | Service account email for GKE nodes |
| kubectl_config_command | Command to configure kubectl |

## 🔧 Module Development

### Best Practices

1. **Versioning**: Use semantic versioning for module releases
2. **Documentation**: Keep README files updated with examples and variable descriptions
3. **Testing**: Test modules in isolation before using in environments
4. **Backwards Compatibility**: Avoid breaking changes when possible

### Adding New Modules

1. Create a new directory under `modules/`
2. Include `main.tf`, `variables.tf`, and `outputs.tf`
3. Add comprehensive documentation
4. Test the module in a development environment
5. Update this README with module information

### Module Structure

```
modules/
└── module-name/
    ├── main.tf          # Main resources
    ├── variables.tf     # Input variables
    ├── outputs.tf       # Output values
    └── README.md        # Module documentation
```

## 🚀 Usage Examples

See the `environments/` directory for real-world usage examples of these modules in different environments (dev, production).

## 📝 Contributing

When contributing to modules:

1. Follow Terraform best practices
2. Use consistent naming conventions
3. Add appropriate variable validation
4. Include comprehensive outputs
5. Update documentation
6. Test changes thoroughly
