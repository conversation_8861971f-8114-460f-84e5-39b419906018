variable "project_id" {
  description = "The GCP project ID"
  type        = string
}

variable "region" {
  description = "The GCP region for resources"
  type        = string
}

variable "network_name" {
  description = "Name of the VPC network"
  type        = string
}

variable "subnet_name" {
  description = "Name of the subnet"
  type        = string
}

variable "subnet_cidr" {
  description = "CIDR block for the subnet"
  type        = string
  default     = "10.0.0.0/24"
}

variable "pods_cidr" {
  description = "CIDR block for pods secondary range"
  type        = string
  default     = "************/22"
}

variable "services_cidr" {
  description = "CIDR block for services secondary range"
  type        = string
  default     = "***********/24"
}

variable "pods_range_name" {
  description = "Name for the pods secondary range"
  type        = string
  default     = "pod-ranges"
}

variable "services_range_name" {
  description = "Name for the services secondary range"
  type        = string
  default     = "services-range"
}

variable "enable_private_google_access" {
  description = "Enable private Google access for the subnet"
  type        = bool
  default     = true
}

variable "enable_flow_logs" {
  description = "Enable VPC flow logs for the subnet"
  type        = bool
  default     = false
}

variable "tags" {
  description = "Tags to apply to resources"
  type        = map(string)
  default     = {}
}
