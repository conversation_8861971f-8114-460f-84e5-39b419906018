# Service Account for GKE nodes (only for standard clusters, not Autopilot)
resource "google_service_account" "gke_service_account" {
  count = var.enable_autopilot ? 0 : 1

  account_id   = "${var.cluster_name}-gke-sa"
  display_name = "GKE Service Account for ${var.cluster_name}"
  project      = var.project_id
}

# IAM bindings for the service account (only for standard clusters)
resource "google_project_iam_member" "gke_service_account_roles" {
  for_each = var.enable_autopilot ? [] : toset([
    "roles/logging.logWriter",
    "roles/monitoring.metricWriter",
    "roles/monitoring.viewer",
    "roles/stackdriver.resourceMetadata.writer"
  ])

  project = var.project_id
  role    = each.value
  member  = "serviceAccount:${google_service_account.gke_service_account[0].email}"
}

# GKE Cluster
resource "google_container_cluster" "primary" {
  name     = var.cluster_name
  location = var.region
  project  = var.project_id
  
  # Enable Autopilot mode if specified
  enable_autopilot = var.enable_autopilot

  # For standard clusters, we remove the default node pool
  # For Autopilot clusters, node pools are managed automatically
  remove_default_node_pool = var.enable_autopilot ? null : true
  initial_node_count       = var.enable_autopilot ? null : 1
  
  network    = var.network_name
  subnetwork = var.subnet_name
  
  # IP allocation policy for VPC-native cluster
  ip_allocation_policy {
    cluster_secondary_range_name  = var.pods_range_name
    services_secondary_range_name = var.services_range_name
  }
  
  # Network policy configuration
  dynamic "network_policy" {
    for_each = var.enable_network_policy ? [1] : []
    content {
      enabled = true
    }
  }
  
  # Workload Identity
  dynamic "workload_identity_config" {
    for_each = var.enable_workload_identity ? [1] : []
    content {
      workload_pool = "${var.project_id}.svc.id.goog"
    }
  }
  
  # Private cluster configuration
  dynamic "private_cluster_config" {
    for_each = var.enable_private_nodes ? [1] : []
    content {
      enable_private_nodes    = var.enable_private_nodes
      enable_private_endpoint = var.enable_private_endpoint
      master_ipv4_cidr_block  = var.master_ipv4_cidr_block
    }
  }
  
  # Master authorized networks
  dynamic "master_authorized_networks_config" {
    for_each = length(var.master_authorized_networks) > 0 ? [1] : []
    content {
      dynamic "cidr_blocks" {
        for_each = var.master_authorized_networks
        content {
          cidr_block   = cidr_blocks.value.cidr_block
          display_name = cidr_blocks.value.display_name
        }
      }
    }
  }
  
  # Addons
  addons_config {
    http_load_balancing {
      disabled = false
    }
    
    horizontal_pod_autoscaling {
      disabled = false
    }
    
    dynamic "network_policy_config" {
      for_each = var.enable_network_policy ? [1] : []
      content {
        disabled = false
      }
    }
    
    gcp_filestore_csi_driver_config {
      enabled = true
    }
  }
  
  # Maintenance policy
  maintenance_policy {
    daily_maintenance_window {
      start_time = var.maintenance_start_time
    }
  }
  
  # Resource usage export for monitoring
  dynamic "resource_usage_export_config" {
    for_each = var.enable_resource_usage_export ? [1] : []
    content {
      enable_network_egress_metering       = true
      enable_resource_consumption_metering = true
      
      bigquery_destination {
        dataset_id = var.bigquery_dataset_id
      }
    }
  }
  
  # Labels
  resource_labels = merge(var.labels, {
    environment = var.environment
    managed_by  = "terraform"
  })
}

# Separately Managed Node Pool (only for standard clusters, not Autopilot)
resource "google_container_node_pool" "primary_nodes" {
  count = var.enable_autopilot ? 0 : 1

  name       = "${var.cluster_name}-node-pool"
  location   = var.region
  cluster    = google_container_cluster.primary.name
  project    = var.project_id
  node_count = var.node_count

  node_config {
    preemptible  = var.preemptible
    machine_type = var.machine_type
    disk_size_gb = var.disk_size_gb
    disk_type    = var.disk_type

    # Service account
    service_account = google_service_account.gke_service_account[0].email
    oauth_scopes    = var.node_pool_oauth_scopes

    # Labels
    labels = merge(var.labels, {
      environment = var.environment
    })

    # Tags
    tags = concat(var.node_pool_tags, ["${var.cluster_name}-node"])

    # Metadata
    metadata = {
      disable-legacy-endpoints = "true"
    }

    # Shielded VM configuration
    dynamic "shielded_instance_config" {
      for_each = var.enable_shielded_nodes ? [1] : []
      content {
        enable_secure_boot          = true
        enable_integrity_monitoring = true
      }
    }
  }

  # Node pool management
  management {
    auto_repair  = true
    auto_upgrade = true
  }

  # Autoscaling
  autoscaling {
    min_node_count = var.min_node_count
    max_node_count = var.max_node_count
  }

  # Upgrade settings
  upgrade_settings {
    max_surge       = 1
    max_unavailable = var.environment == "production" ? 0 : 1
  }
}
