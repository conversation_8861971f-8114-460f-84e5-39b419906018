output "cluster_name" {
  description = "GKE cluster name"
  value       = google_container_cluster.primary.name
}

output "cluster_id" {
  description = "GKE cluster ID"
  value       = google_container_cluster.primary.id
}

output "cluster_endpoint" {
  description = "GKE cluster endpoint"
  value       = google_container_cluster.primary.endpoint
  sensitive   = true
}

output "cluster_location" {
  description = "GKE cluster location"
  value       = google_container_cluster.primary.location
}

output "cluster_ca_certificate" {
  description = "GKE cluster CA certificate"
  value       = google_container_cluster.primary.master_auth.0.cluster_ca_certificate
  sensitive   = true
}

output "cluster_master_version" {
  description = "GKE cluster master version"
  value       = google_container_cluster.primary.master_version
}

output "cluster_node_version" {
  description = "GKE cluster node version"
  value       = google_container_cluster.primary.node_version
}

output "service_account_email" {
  description = "Service account email for GKE nodes"
  value       = google_service_account.gke_service_account.email
}

output "service_account_name" {
  description = "Service account name for GKE nodes"
  value       = google_service_account.gke_service_account.name
}

output "node_pool_name" {
  description = "Name of the primary node pool"
  value       = google_container_node_pool.primary_nodes.name
}

output "node_pool_instance_group_urls" {
  description = "Instance group URLs of the node pool"
  value       = google_container_node_pool.primary_nodes.instance_group_urls
}

output "kubectl_config_command" {
  description = "Command to configure kubectl"
  value       = "gcloud container clusters get-credentials ${google_container_cluster.primary.name} --region ${google_container_cluster.primary.location} --project ${var.project_id}"
}

output "cluster_self_link" {
  description = "GKE cluster self link"
  value       = google_container_cluster.primary.self_link
}
