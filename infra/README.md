# File Management System - Infrastructure

This directory contains Terraform configurations for deploying GKE clusters across multiple environments using a modular architecture.

## 🏗️ Architecture Overview

### Module-Based Architecture

The infrastructure is organized using reusable Terraform modules:

- **VPC Module** (`modules/vpc/`): Creates VPC networks, subnets, and networking components
- **GKE Module** (`modules/gke/`): Creates GKE clusters, node pools, and service accounts

### Environments

- **Dev**: Development environment with cost-optimized settings
- **Production**: Production environment with high availability and security features
- **Global**: Shared resources and global configurations

### Infrastructure Components

#### Development Environment (`infra/environments/dev/`)

- **GKE Cluster**: `file-management-dev`
- **Machine Type**: `e2-medium` (cost-effective)
- **Node Count**: 2 nodes (auto-scaling 1-5)
- **Disk**: 20GB standard persistent disk
- **Features**: Preemptible instances for cost savings

#### Production Environment (`infra/environments/production/`)

- **GKE Cluster**: `file-management-prod`
- **Machine Type**: `e2-standard-2` (higher performance)
- **Node Count**: 3 nodes (auto-scaling 2-10)
- **Disk**: 50GB SSD persistent disk
- **Features**:
  - Private cluster configuration
  - Shielded VM instances
  - Cloud NAT for egress traffic
  - Zero-downtime upgrades
  - Resource usage monitoring

## 🚀 Deployment Instructions

### Prerequisites

1. **Google Cloud SDK**: Install and authenticate

   ```bash
   gcloud auth login
   gcloud config set project YOUR_PROJECT_ID
   ```

2. **Terraform**: Install Terraform >= 1.0

   ```bash
   # macOS
   brew install terraform

   # Or download from https://terraform.io/downloads
   ```

3. **Enable Required APIs**:

   ```bash
   gcloud services enable container.googleapis.com
   gcloud services enable compute.googleapis.com
   ```

4. **Create GCS Bucket for State** (if not already created):
   ```bash
   gsutil mb -p YOUR_PROJECT_ID gs://tf-state-backend-practice
   gsutil versioning set on gs://tf-state-backend-practice
   ```

### Configuration

1. **Update Project ID**: Edit the `project_id` variable in each environment's `variables.tf`:

   ```hcl
   variable "project_id" {
     default = "your-actual-project-id"
   }
   ```

2. **Customize Settings**: Modify variables in `variables.tf` files as needed for your requirements.

### Deploy Development Environment

```bash
cd infra/environments/dev

# Initialize Terraform
terraform init

# Review the plan
terraform plan

# Apply the configuration
terraform apply
```

### Deploy Production Environment

```bash
cd infra/environments/production

# Initialize Terraform
terraform init

# Review the plan
terraform plan

# Apply the configuration
terraform apply
```

## 🔧 Post-Deployment Setup

### Configure kubectl

After deployment, configure kubectl to connect to your clusters:

```bash
# For development
gcloud container clusters get-credentials file-management-dev --region us-central1 --project YOUR_PROJECT_ID

# For production
gcloud container clusters get-credentials file-management-prod --region us-central1 --project YOUR_PROJECT_ID
```

### Verify Cluster Access

```bash
# Check cluster info
kubectl cluster-info

# List nodes
kubectl get nodes

# Check system pods
kubectl get pods -n kube-system
```

## 📊 Monitoring and Management

### Cluster Monitoring

- **Google Cloud Console**: Navigate to Kubernetes Engine
- **kubectl**: Use standard kubectl commands
- **Logs**: Available in Cloud Logging

### Cost Optimization

- **Dev Environment**: Uses preemptible instances and smaller machine types
- **Production Environment**: Optimized for performance and reliability

### Security Features

- **Workload Identity**: Enabled for secure pod-to-GCP service communication
- **Network Policies**: Enabled for pod-to-pod communication control
- **Private Clusters**: Production uses private nodes
- **Shielded VMs**: Enabled in production for additional security

## 🧩 Module Details

### VPC Module (`modules/vpc/`)

Creates and manages VPC networking components:

- **VPC Network**: Custom VPC with no auto-created subnets
- **Subnet**: Regional subnet with secondary IP ranges for GKE
- **Firewall Rules**: Internal communication and SSH access rules
- **Cloud Router & NAT**: For private cluster egress traffic
- **Configurable CIDR blocks**: Separate ranges for different environments

**Key Features:**

- Configurable CIDR blocks for subnets, pods, and services
- Optional VPC flow logs for monitoring
- Private Google Access enabled by default
- Automatic firewall rules for GKE communication

### GKE Module (`modules/gke/`)

Creates and manages GKE clusters and node pools:

- **GKE Cluster**: Regional cluster with VPC-native networking
- **Node Pool**: Managed node pool with autoscaling
- **Service Account**: Custom service account with minimal required permissions
- **Security Features**: Configurable private clusters, shielded VMs, workload identity

**Key Features:**

- Environment-specific configurations (dev vs production)
- Configurable machine types, disk sizes, and node counts
- Auto-scaling and auto-upgrade capabilities
- Security hardening options (private clusters, shielded VMs)
- Resource usage monitoring (optional)

## 🔄 Maintenance

### Updating Clusters

```bash
# Navigate to environment directory
cd infra/environments/[dev|production]

# Update configuration
terraform plan
terraform apply
```

### Destroying Resources

```bash
# ⚠️ WARNING: This will destroy all resources
terraform destroy
```

## 📁 Directory Structure

```
infra/
├── modules/                 # Reusable Terraform modules
│   ├── vpc/                # VPC networking module
│   │   ├── main.tf         # VPC resources
│   │   ├── variables.tf    # VPC input variables
│   │   └── outputs.tf      # VPC outputs
│   └── gke/                # GKE cluster module
│       ├── main.tf         # GKE cluster and node pool resources
│       ├── variables.tf    # GKE input variables
│       └── outputs.tf      # GKE outputs
├── environments/           # Environment-specific configurations
│   ├── dev/
│   │   ├── main.tf         # Dev environment using modules
│   │   ├── variables.tf    # Dev environment variables
│   │   └── outputs.tf      # Dev environment outputs
│   ├── production/
│   │   ├── main.tf         # Production environment using modules
│   │   ├── variables.tf    # Production environment variables
│   │   └── outputs.tf      # Production environment outputs
│   └── global/
│       └── main.tf         # Global backend configuration
├── deploy.sh               # Deployment automation script
├── terraform.tfvars.example # Example variables file
└── README.md               # This file
```

## 🆘 Troubleshooting

### Common Issues

1. **Permission Errors**: Ensure your account has necessary IAM roles
2. **API Not Enabled**: Enable required Google Cloud APIs
3. **Quota Limits**: Check your project quotas in Google Cloud Console
4. **State Lock**: If using Terraform Cloud, check for state locks

### Getting Help

- **Terraform Docs**: https://terraform.io/docs
- **GKE Documentation**: https://cloud.google.com/kubernetes-engine/docs
- **Google Cloud Support**: Available through Google Cloud Console

## 🔐 Security Considerations

- Store sensitive variables in environment variables or secret management systems
- Regularly update Terraform providers and modules
- Monitor cluster access and audit logs
- Implement proper RBAC policies
- Keep node images updated through auto-upgrade features
